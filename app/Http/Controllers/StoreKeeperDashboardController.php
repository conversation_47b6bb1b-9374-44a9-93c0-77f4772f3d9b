<?php

namespace App\Http\Controllers;

use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class StoreKeeperDashboardController extends Controller
{
    /**
     * Display the store keeper dashboard.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();
        
        // Ensure user has store keeper permissions
        if (!$user->can('store-keep')) {
            abort(403, 'Unauthorized access to store keeper dashboard');
        }

        // Get user's organization for data scoping
        $organizationIds = $user->organizations->pluck('id')->toArray();
        
        if (empty($organizationIds)) {
            abort(403, 'No organization access found');
        }

        $stats = [
            'pending_approvals' => $this->getPendingApprovalsCount($organizationIds, $user->id),
            'overdue_approvals' => $this->getOverdueApprovalsCount($organizationIds),
            'avg_approval_time_hours' => $this->getAverageApprovalTime($organizationIds),
            'longest_pending_hours' => $this->getLongestPendingTime($organizationIds),

            'inventory_items' => $this->getInventoryItemsCount($organizationIds),
            'low_stock_alerts' => $this->getLowStockCount($organizationIds),
            'critical_stock_alerts' => $this->getCriticalStockCount($organizationIds),
            'out_of_stock_items' => $this->getOutOfStockCount($organizationIds),

            'pending_issues' => $this->getPendingIssuesCount($organizationIds),
            'partial_issues' => $this->getPartialIssuesCount($organizationIds),
            'ready_to_issue' => $this->getPendingIssuesCount($organizationIds) + $this->getPartialIssuesCount($organizationIds),

            'todays_transactions' => $this->getTodaysTransactionsCount($organizationIds),
            'weekly_transactions' => $this->getWeeklyTransactionsCount($organizationIds),
            'weekly_requisition_trend' => $this->getWeeklyRequisitionTrend($organizationIds),

            'categories_count' => $this->getCategoriesCount($organizationIds),
            'total_inventory_value' => $this->getTotalInventoryValue($organizationIds)
        ];

        $pendingApprovals = $this->getPendingApprovals($organizationIds, $user->id);
        $inventoryAlerts = $this->getInventoryAlerts($organizationIds);
        $recentActivity = $this->getRecentActivity($organizationIds, $user->id);
        $topRequestedItems = $this->getTopRequestedItems($organizationIds);
        $stockVsDemandData = $this->getStockVsDemandData($organizationIds);

        return Inertia::render('StoreKeeper/Dashboard', [
            'stats' => $stats,
            'pendingApprovals' => $pendingApprovals,
            'inventoryAlerts' => $inventoryAlerts,
            'recentActivity' => $recentActivity,
            'topRequestedItems' => $topRequestedItems,
            'stockVsDemandData' => $stockVsDemandData,
            'user' => [
                'id' => $user->id,
                'name' => $user->name ?? $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                'roles' => $user->getRoleNames()->toArray(),
            ],
        ]);
    }

    public function pendingApprovals(Request $request)
    {
        $user = Auth::user();
        $organizationIds = $user->organizations->pluck('id')->toArray();
        
        $pendingApprovals = $this->getPendingApprovals($organizationIds, $user->id, 20);
        
        return response()->json([
            'pending_approvals' => $pendingApprovals,
            'total_count' => $this->getPendingApprovalsCount($organizationIds, $user->id)
        ]);
    }

    /**
     * Get inventory alerts for low stock items.
     */
    public function inventoryAlerts(Request $request)
    {
        $user = Auth::user();
        $organizationIds = $user->organizations->pluck('id')->toArray();
        
        $alerts = $this->getInventoryAlerts($organizationIds, 20);
        
        return response()->json([
            'inventory_alerts' => $alerts,
            'total_count' => $this->getLowStockCount($organizationIds)
        ]);
    }

    /**
     * Get recent activity feed.
     */
    public function recentActivity(Request $request)
    {
        $user = Auth::user();
        $organizationIds = $user->organizations->pluck('id')->toArray();
        
        $activity = $this->getRecentActivity($organizationIds, $user->id, 20);
        
        return response()->json([
            'recent_activity' => $activity
        ]);
    }

    /**
     * Get count of pending approvals (excluding own requisitions).
     */
    private function getPendingApprovalsCount(array $organizationIds, int $userId): int
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->where('requester_user_id', '!=', $userId) // Exclude own requisitions
            ->count();
    }

    /**
     * Get total inventory items count.
     */
    private function getInventoryItemsCount(array $organizationIds): int
    {
        return InventoryItem::whereIn('organization_id', $organizationIds)->count();
    }

    /**
     * Get count of low stock items.
     */
    private function getLowStockCount(array $organizationIds): int
    {
        return InventoryItem::whereIn('organization_id', $organizationIds)
            ->whereRaw('quantity_on_hand <= reorder_level')
            ->where('reorder_level', '>', 0)
            ->count();
    }

    /**
     * Get count of store keeper's own requisitions.
     */
    private function getMyRequisitionsCount(int $userId): int
    {
        return StoreRequisition::where('requester_user_id', $userId)->count();
    }



    /**
     * Get count of approved requisitions awaiting fulfillment.
     */
    private function getAwaitingFulfillmentCount(array $organizationIds): int
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_APPROVED)
            ->count();
    }

    /**
     * Get count of requisitions ready for issuing.
     */
    private function getPendingIssuesCount(array $organizationIds): int
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_APPROVED)
            ->count();
    }

    /**
     * Get count of partially issued requisitions.
     */
    private function getPartialIssuesCount(array $organizationIds): int
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_PARTIALLY_ISSUED)
            ->count();
    }

    /**
     * Get total inventory value based on quantity on hand.
     */
    private function getTotalInventoryValue(array $organizationIds): float
    {
        // Calculate total value based on quantity on hand
        // Using a simple calculation since we don't have cost fields yet
        return \App\Models\InventoryItem::whereIn('organization_id', $organizationIds)
            ->sum('quantity_on_hand');
    }

    /**
     * Get count of today's inventory transactions.
     */
    private function getTodaysTransactionsCount(array $organizationIds): int
    {
        return \App\Models\InventoryTransaction::whereHas('inventoryItem', function ($query) use ($organizationIds) {
                $query->whereIn('organization_id', $organizationIds);
            })
            ->whereDate('transaction_date', today())
            ->count();
    }

    /**
     * Get count of inventory categories.
     */
    private function getCategoriesCount(array $organizationIds): int
    {
        // Count distinct categories from inventory items
        return \App\Models\InventoryItem::whereIn('organization_id', $organizationIds)
            ->distinct('category')
            ->whereNotNull('category')
            ->where('category', '!=', '')
            ->count('category');
    }



    /**
     * Get pending approvals with details.
     */
    private function getPendingApprovals(array $organizationIds, int $userId, int $limit = 10): array
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->where('requester_user_id', '!=', $userId) // Exclude own requisitions
            ->with(['requester:id,first_name,last_name,email', 'department:id,name', 'items.inventoryItem:id,name,sku'])
            ->orderBy('requested_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($requisition) {
                return [
                    'id' => $requisition->id,
                    'purpose' => $requisition->purpose,
                    'status' => $requisition->status,
                    'requested_at' => $requisition->requested_at,
                    'requester' => $requisition->requester ? [
                        'id' => $requisition->requester->id,
                        'name' => $requisition->requester->first_name . ' ' . $requisition->requester->last_name,
                        'email' => $requisition->requester->email,
                    ] : null,
                    'department' => $requisition->department ? [
                        'id' => $requisition->department->id,
                        'name' => $requisition->department->name,
                    ] : null,
                    'items_count' => $requisition->items->count(),
                    'items_preview' => $requisition->items->take(3)->map(function ($item) {
                        return [
                            'name' => $item->inventoryItem->name ?? 'Unknown Item',
                            'sku' => $item->inventoryItem->sku ?? '',
                            'quantity_requested' => $item->quantity_requested,
                        ];
                    }),
                ];
            })
            ->toArray();
    }

    /**
     * Get inventory alerts for low stock items.
     */
    private function getInventoryAlerts(array $organizationIds, int $limit = 10): array
    {
        return InventoryItem::whereIn('organization_id', $organizationIds)
            ->whereRaw('quantity_on_hand <= reorder_level')
            ->where('reorder_level', '>', 0)
            ->with(['branch:id,name'])
            ->orderByRaw('(quantity_on_hand / NULLIF(reorder_level, 0)) ASC') // Most critical first
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'sku' => $item->sku,
                    'name' => $item->name,
                    'quantity_on_hand' => $item->quantity_on_hand,
                    'reorder_level' => $item->reorder_level,
                    'unit_of_measure' => $item->unit_of_measure,
                    'branch' => $item->branch ? [
                        'id' => $item->branch->id,
                        'name' => $item->branch->name,
                    ] : null,
                    'is_out_of_stock' => $item->isOutOfStock(),
                    'stock_percentage' => $item->reorder_level > 0 
                        ? round(($item->quantity_on_hand / $item->reorder_level) * 100, 1)
                        : 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get recent activity feed.
     */
    private function getRecentActivity(array $organizationIds, int $userId, int $limit = 10): array
    {
        // Get recent approvals by this store keeper
        $recentApprovals = StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('approver_user_id', $userId)
            ->whereNotNull('approved_at')
            ->with(['requester:id,first_name,last_name'])
            ->orderBy('approved_at', 'desc')
            ->limit($limit / 2)
            ->get()
            ->map(function ($requisition) {
                return [
                    'type' => 'approval',
                    'action' => $requisition->status === StoreRequisition::STATUS_APPROVED ? 'approved' : 'rejected',
                    'description' => 'Store Requisition #' . $requisition->id,
                    'user' => $requisition->requester ? 
                        $requisition->requester->first_name . ' ' . $requisition->requester->last_name : 
                        'Unknown User',
                    'timestamp' => $requisition->approved_at,
                    'link' => '/store-requisitions/' . $requisition->id,
                ];
            });

        // Get recent inventory transactions
        $recentTransactions = InventoryTransaction::whereHas('inventoryItem', function ($query) use ($organizationIds) {
                $query->whereIn('organization_id', $organizationIds);
            })
            ->with(['inventoryItem:id,name,sku', 'user:id,first_name,last_name'])
            ->orderBy('transaction_date', 'desc')
            ->limit($limit / 2)
            ->get()
            ->map(function ($transaction) {
                return [
                    'type' => 'inventory',
                    'action' => $transaction->transaction_type,
                    'description' => $transaction->inventoryItem->name ?? 'Unknown Item',
                    'quantity' => $transaction->quantity_change,
                    'user' => $transaction->user ? 
                        $transaction->user->first_name . ' ' . $transaction->user->last_name : 
                        'System',
                    'timestamp' => $transaction->transaction_date,
                    'link' => '/inventory/' . $transaction->inventory_item_id,
                ];
            });

        // Combine and sort by timestamp
        $allActivity = collect($recentApprovals)->concat($recentTransactions)
            ->sortByDesc('timestamp')
            ->take($limit)
            ->values()
            ->toArray();

        return $allActivity;
    }

    /**
     * Get top requested inventory items with status breakdown.
     */
    private function getTopRequestedItems(array $organizationIds, int $limit = 5): array
    {
        // Get all submitted requests (exclude only drafts)
        $allRequests = \App\Models\StoreRequisitionItem::whereHas('storeRequisition', function ($query) use ($organizationIds) {
                $query->whereIn('organization_id', $organizationIds)
                      ->whereNotIn('status', [StoreRequisition::STATUS_DRAFT]); // Exclude only drafts
            })
            ->with(['inventoryItem:id,name,sku,unit_of_measure', 'storeRequisition:id,status'])
            ->selectRaw('inventory_item_id, COUNT(*) as total_request_count, SUM(quantity_requested) as total_quantity')
            ->groupBy('inventory_item_id')
            ->orderByDesc('total_request_count')
            ->limit($limit)
            ->get();

        // Get status breakdown for each top item
        return $allRequests->map(function ($item) {
            $inventoryItemId = $item->inventory_item_id;

            // Get status breakdown
            $statusBreakdown = \App\Models\StoreRequisitionItem::whereHas('storeRequisition', function ($query) use ($inventoryItemId) {
                    $query->where('inventory_item_id', $inventoryItemId)
                          ->whereNotIn('status', [StoreRequisition::STATUS_DRAFT]);
                })
                ->join('store_requisitions', 'store_requisition_items.store_requisition_id', '=', 'store_requisitions.id')
                ->selectRaw('store_requisitions.status, COUNT(*) as count, SUM(store_requisition_items.quantity_requested) as quantity')
                ->where('store_requisition_items.inventory_item_id', $inventoryItemId)
                ->groupBy('store_requisitions.status')
                ->get()
                ->keyBy('status');

            // Calculate approval rate
            $approvedCount = ($statusBreakdown[StoreRequisition::STATUS_APPROVED]->count ?? 0) +
                           ($statusBreakdown[StoreRequisition::STATUS_ISSUED]->count ?? 0) +
                           ($statusBreakdown[StoreRequisition::STATUS_PARTIALLY_ISSUED]->count ?? 0);

            $approvalRate = $item->total_request_count > 0 ? round(($approvedCount / $item->total_request_count) * 100, 1) : 0;

            return [
                'inventory_item_id' => $item->inventory_item_id,
                'name' => $item->inventoryItem->name ?? 'Unknown Item',
                'sku' => $item->inventoryItem->sku ?? '',
                'unit_of_measure' => $item->inventoryItem->unit_of_measure ?? 'unit',
                'total_request_count' => $item->total_request_count,
                'approved_count' => $approvedCount,
                'total_quantity' => $item->total_quantity,
                'approval_rate' => $approvalRate,
                'status_breakdown' => [
                    'pending_approval' => $statusBreakdown[StoreRequisition::STATUS_PENDING_APPROVAL]->count ?? 0,
                    'approved' => $statusBreakdown[StoreRequisition::STATUS_APPROVED]->count ?? 0,
                    'rejected' => $statusBreakdown[StoreRequisition::STATUS_REJECTED]->count ?? 0,
                    'issued' => $statusBreakdown[StoreRequisition::STATUS_ISSUED]->count ?? 0,
                    'partially_issued' => $statusBreakdown[StoreRequisition::STATUS_PARTIALLY_ISSUED]->count ?? 0,
                    'returned_for_revision' => $statusBreakdown[StoreRequisition::STATUS_RETURNED_FOR_REVISION]->count ?? 0,
                ],
            ];
        })->toArray();
    }

    /**
     * Get count of overdue approvals (pending for more than 24 hours).
     */
    private function getOverdueApprovalsCount(array $organizationIds): int
    {
        return StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->where('requested_at', '<', now()->subDay())
            ->count();
    }

    /**
     * Get average approval processing time in hours.
     */
    private function getAverageApprovalTime(array $organizationIds): float
    {
        $requisitions = StoreRequisition::whereIn('organization_id', $organizationIds)
            ->whereNotNull('requested_at')
            ->whereNotNull('approved_at')
            ->select('requested_at', 'approved_at')
            ->get();

        if ($requisitions->isEmpty()) {
            return 0;
        }

        $totalSeconds = $requisitions->sum(function ($requisition) {
            return $requisition->approved_at->diffInSeconds($requisition->requested_at);
        });

        $avgSeconds = $totalSeconds / $requisitions->count();
        return round($avgSeconds / 3600, 1); // Convert to hours
    }

    /**
     * Get longest pending approval time in hours.
     */
    private function getLongestPendingTime(array $organizationIds): float
    {
        $oldestRequest = StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->whereNotNull('requested_at')
            ->orderBy('requested_at', 'asc')
            ->first();

        if (!$oldestRequest || !$oldestRequest->requested_at) {
            return 0;
        }

        return round($oldestRequest->requested_at->diffInHours(now()), 1);
    }

    /**
     * Get count of critical stock items (below 25% of reorder level).
     */
    private function getCriticalStockCount(array $organizationIds): int
    {
        return InventoryItem::whereIn('organization_id', $organizationIds)
            ->whereRaw('quantity_on_hand <= (reorder_level * 0.25)')
            ->where('reorder_level', '>', 0)
            ->count();
    }

    /**
     * Get count of out of stock items.
     */
    private function getOutOfStockCount(array $organizationIds): int
    {
        return InventoryItem::whereIn('organization_id', $organizationIds)
            ->where('quantity_on_hand', '<=', 0)
            ->count();
    }

    /**
     * Get weekly transactions count.
     */
    private function getWeeklyTransactionsCount(array $organizationIds): int
    {
        return InventoryTransaction::whereHas('inventoryItem', function ($query) use ($organizationIds) {
                $query->whereIn('organization_id', $organizationIds);
            })
            ->where('transaction_date', '>=', now()->startOfWeek())
            ->count();
    }

    /**
     * Get weekly requisition trend (percentage change from last week).
     */
    private function getWeeklyRequisitionTrend(array $organizationIds): float
    {
        $thisWeek = StoreRequisition::whereIn('organization_id', $organizationIds)
            ->where('created_at', '>=', now()->startOfWeek())
            ->count();

        $lastWeek = StoreRequisition::whereIn('organization_id', $organizationIds)
            ->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])
            ->count();

        if ($lastWeek === 0) return $thisWeek > 0 ? 100 : 0;

        return round((($thisWeek - $lastWeek) / $lastWeek) * 100, 1);
    }

    /**
     * Get top 10 items with stock vs demand data for the horizontal bar chart.
     */
    private function getStockVsDemandData(array $organizationIds, int $limit = 10): array
    {
        // Get top requested items in the last 30 days
        $topItems = \App\Models\StoreRequisitionItem::whereHas('storeRequisition', function ($query) use ($organizationIds) {
                $query->whereIn('organization_id', $organizationIds)
                      ->where('created_at', '>=', now()->subDays(30))
                      ->whereNotIn('status', [StoreRequisition::STATUS_DRAFT]);
            })
            ->with(['inventoryItem:id,name,sku,unit_of_measure,quantity_on_hand,reorder_level'])
            ->selectRaw('inventory_item_id, COUNT(*) as request_count, SUM(quantity_requested) as total_requested')
            ->groupBy('inventory_item_id')
            ->orderByDesc('total_requested')
            ->limit($limit)
            ->get();

        return $topItems->map(function ($item) {
            $inventoryItem = $item->inventoryItem;

            if (!$inventoryItem) {
                return null;
            }

            // Calculate stock health status
            $stockStatus = 'healthy';
            if ($inventoryItem->quantity_on_hand <= 0) {
                $stockStatus = 'out_of_stock';
            } elseif ($inventoryItem->quantity_on_hand <= ($inventoryItem->reorder_level * 0.25)) {
                $stockStatus = 'critical';
            } elseif ($inventoryItem->quantity_on_hand <= $inventoryItem->reorder_level) {
                $stockStatus = 'low';
            }

            // Calculate days of stock remaining based on recent demand
            $dailyDemand = $item->total_requested / 30; // Average daily demand over 30 days
            $daysRemaining = $dailyDemand > 0 ? round($inventoryItem->quantity_on_hand / $dailyDemand, 1) : 999;

            return [
                'inventory_item_id' => $inventoryItem->id,
                'name' => $inventoryItem->name,
                'sku' => $inventoryItem->sku,
                'unit_of_measure' => $inventoryItem->unit_of_measure,
                'current_stock' => $inventoryItem->quantity_on_hand,
                'monthly_demand' => $item->total_requested,
                'request_count' => $item->request_count,
                'stock_status' => $stockStatus,
                'days_remaining' => min($daysRemaining, 999), // Cap at 999 for display
                'reorder_level' => $inventoryItem->reorder_level,
            ];
        })->filter()->values()->toArray(); // Remove null entries and reindex
    }
}
